import requests
from typing import List

API_URL = "https://n1n.ai/v1/chat/completions"
API_KEY = "sk-C1JOCftnu4hY9DbMC9tphMoW6T1TjWgSgidJWlxjs07o1ilu"

HEADERS = {
    "Content-Type": "application/json",
    "Authorization": f"Bearer {API_KEY}"
}

def batch_generation(system_messages: List[str], prompts: List[str], max_new_tokens: int = 256, temperature: float = 0.6) -> List[str]:
    results = []
    for system_message, prompt in zip(system_messages, prompts):
        payload = {
            "model": "gpt-3.5-turbo",
            "messages": [
                {"role": "system", "content": system_message},
                {"role": "user", "content": prompt}
            ],
            "max_tokens": max_new_tokens,
            "temperature": temperature,
            "stream": False
        }
        response = requests.post(API_URL, headers=HEADERS, json=payload)
        response.raise_for_status()
        data = response.json()
        if "choices" in data and len(data["choices"]) > 0:
            results.append(data["choices"][0]["message"]["content"])
        else:
            results.append("")
    return results
