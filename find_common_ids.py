import pandas as pd

# 路径根据你的项目结构调整
votes_path = 'data/howTheyVoteDataSet/votes.csv'
speeches_path = 'data/debates/counterfactual_speeches.csv'

# 读取数据
votes = pd.read_csv(votes_path)
speeches = pd.read_csv(speeches_path)

# 取出id列，转为集合
vote_ids = set(votes['id'])
speech_ids = set(speeches['id'])

# 求交集
common_ids = vote_ids & speech_ids

if common_ids:
    print(f"可用于 vote_list 的 id 有 {len(common_ids)} 个，全部如下：")
    for cid in sorted(common_ids):
        print(cid)
else:
    print("没有找到 votes.csv 和 counterfactual_speeches.csv 都有的 id，请检查数据！")
